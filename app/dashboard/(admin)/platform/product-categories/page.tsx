import { ProductCategoriesListPage } from '@/features/platform/categories/pages/ProductCategoriesListPage';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton'; // A generic skeleton for the page

// Define a more specific skeleton for the list page content if desired
function PageSkeleton() {
  return (
    <div className="h-screen overflow-hidden flex flex-col">
      {/* Skeleton for PageBreadcrumbs */}
      <div className="sticky top-0 z-30 bg-background flex h-16 shrink-0 items-center gap-2 border-b px-4">
        <Skeleton className="h-6 w-1/4" />
        <div className="ml-auto">
            <Skeleton className="h-8 w-32" />
        </div>
      </div>
      
      <div className="flex flex-col flex-1 min-h-0">
        {/* Skeleton for Title Area */}
        <div className="border-b border-gray-200 dark:border-gray-800">
          <div className="px-4 md:px-6 pt-4 md:pt-6 pb-4">
            <Skeleton className="h-8 w-1/3 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </div>

        {/* Skeleton for PlatformFilterBar */}
        <div className="p-4 border-b">
            <Skeleton className="h-10 w-full" />
        </div>

        {/* Skeleton for StatusTabs */}
        <div className="p-4 border-b">
            <Skeleton className="h-10 w-full" />
        </div>

        {/* Skeleton for List Content */}
        <div className="flex-1 overflow-auto p-4">
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-20 w-full rounded-md" />
            ))}
          </div>
        </div>

        {/* Skeleton for PaginationWrapper */}
        <div className="p-4 border-t">
            <Skeleton className="h-10 w-full" />
        </div>
      </div>
    </div>
  );
}

export default async function ProductCategoryRoutePage({ searchParams }: { searchParams: { [key: string]: string | string[] | undefined } }) {
  // The ProductCategoriesListPage is a Server Component and handles its own data fetching.
  // We pass searchParams to it so it can control pagination, filtering, etc.
  return (
    <Suspense fallback={<PageSkeleton />}>
      <ProductCategoriesListPage searchParams={searchParams} />
    </Suspense>
  );
}