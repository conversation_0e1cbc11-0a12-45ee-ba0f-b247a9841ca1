// @ts-ignore - Using JavaScript version for compatibility
import { CustomFields } from "./CustomFields";
// @ts-ignore - JavaScript module
import * as RelationshipView from "./views/Relationship";
import type { FieldMeta } from "@/features/dashboard/types";
import type { Value } from "@/features/dashboard/lib/useChangedFieldsAndDataForUpdate";

interface MediaTabProps {
  fields: Record<string, FieldMeta>;
  value: Record<string, Value>;
  onChange: (valueUpdater: (prevValue: Record<string, Value>) => Record<string, Value>) => void;
  forceValidation: boolean;
  invalidFields: ReadonlySet<string>;
  fieldModes?: Record<string, "read" | "edit" | "hidden"> | null;
  fieldPositions?: Record<string, "form" | "sidebar"> | null;
}

export function MediaTab({
  fields,
  value,
  onChange,
  forceValidation,
  invalidFields,
  fieldModes,
  fieldPositions,
}: MediaTabProps) {
  // Define custom views for specific fields by path
  const fieldViews = {
    productImages: RelationshipView,
  };

  // Adapt the onChange function to match what CustomFields expects
  const adaptedOnChange = (valueUpdater: (prevValue: Record<string, Value>) => Record<string, Value>) => {
    onChange((prevValue: Record<string, Value>) => valueUpdater(prevValue));
  };

  return (
    <CustomFields
      fields={fields}
      value={value}
      onChange={adaptedOnChange}
      forceValidation={forceValidation}
      invalidFields={invalidFields}
      fieldModes={fieldModes as any}
      fieldPositions={fieldPositions as any}
      fieldViews={fieldViews}
    />
  );
}
