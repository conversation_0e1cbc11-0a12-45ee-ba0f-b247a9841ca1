'use server';

import { revalidatePath } from 'next/cache';
import { keystoneClient } from "@/features/dashboard/lib/keystoneClient";

/**
 * Create product variant
 */
export async function createProductVariant(data: {
  title: string;
  sku?: string;
  barcode?: string;
  ean?: string;
  upc?: string;
  material?: string;
  inventoryQuantity?: number;
  manageInventory?: boolean;
  allowBackorder?: boolean;
  hsCode?: string;
  originCountry?: string;
  midCode?: string;
  productId: string;
  optionValueIds: string[];
  prices?: Array<{
    amount: number;
    compareAmount?: number;
    currencyCode: string;
    regionCode?: string;
  }>;
}) {
  const query = `
    mutation CreateProductVariant($data: ProductVariantCreateInput!) {
      createProductVariant(data: $data) {
        id
        title
        sku
        barcode
        ean
        upc
        material
        productOptionValues {
          id
          value
          productOption {
            id
            title
          }
        }
        prices {
          id
          amount
          compareAmount
          region {
            id
            code
            name
            currency {
              code
              symbol
              symbolNative
            }
          }
        }
      }
    }
  `;

  const prices = data.prices?.map((price) => ({
    amount: price.amount,
    compareAmount: price.compareAmount,
    currency: {
      connect: { code: price.currencyCode.toLowerCase() },
    },
    ...(price.regionCode
      ? {
          region: {
            connect: { code: price.regionCode },
          },
        }
      : {}),
  })) || [];

  const response = await keystoneClient(query, {
    data: {
      title: data.title,
      sku: data.sku || "",
      barcode: data.barcode || "",
      ean: data.ean || "",
      upc: data.upc || "",
      material: data.material || "",
      inventoryQuantity: data.inventoryQuantity || 100,
      manageInventory: data.manageInventory || false,
      allowBackorder: data.allowBackorder || false,
      hsCode: data.hsCode || "",
      originCountry: data.originCountry || "",
      midCode: data.midCode || "",
      product: { connect: { id: data.productId } },
      productOptionValues: {
        connect: data.optionValueIds.map((id: string) => ({ id })),
      },
      prices: { create: prices },
    },
  });

  if (response.success) {
    revalidatePath(`/dashboard/platform/products/${data.productId}`);
  }

  return response;
}

/**
 * Delete product variant
 */
export async function deleteProductVariant(id: string) {
  // First get the product ID for revalidation
  const productQuery = `
    query GetProductFromVariant($id: ID!) {
      productVariant(where: { id: $id }) {
        product {
          id
        }
      }
    }
  `;
  
  const productResponse = await keystoneClient(productQuery, { id });
  const productId = productResponse.success ? productResponse.data.productVariant?.product?.id : null;

  const query = `
    mutation DeleteProductVariant($id: ID!) {
      deleteProductVariant(where: { id: $id }) {
        id
        title
      }
    }
  `;

  const response = await keystoneClient(query, { id });

  if (response.success && productId) {
    revalidatePath(`/dashboard/platform/products/${productId}`);
  }

  return response;
}

/**
 * Update variant price (add new price to variant)
 */
export async function updateVariantPrice(variantId: string, priceData: {
  amount: number;
  compareAmount?: number;
  currencyCode: string;
  regionCode?: string;
}) {
  const query = `
    mutation AddVariantPrice($id: ID!, $input: ProductVariantUpdateInput!) {
      updateProductVariant(where: { id: $id }, data: $input) {
        id
        prices {
          id
          amount
          compareAmount
          currency {
            code
            symbol
            name
            symbolNative
          }
          region {
            id
            code
            name
            taxRate
          }
        }
      }
    }
  `;

  const response = await keystoneClient(query, {
    id: variantId,
    input: {
      prices: {
        create: [
          {
            amount: priceData.amount,
            compareAmount: priceData.compareAmount,
            currency: {
              connect: { code: priceData.currencyCode.toLowerCase() },
            },
            ...(priceData.regionCode
              ? {
                  region: {
                    connect: { code: priceData.regionCode },
                  },
                }
              : {}),
          },
        ],
      },
    },
  });

  if (response.success) {
    // Get product ID for revalidation
    const productQuery = `
      query GetProductFromVariant($id: ID!) {
        productVariant(where: { id: $id }) {
          product {
            id
          }
        }
      }
    `;
    
    const productResponse = await keystoneClient(productQuery, { id: variantId });
    if (productResponse.success && productResponse.data.productVariant?.product?.id) {
      revalidatePath(`/dashboard/platform/products/${productResponse.data.productVariant.product.id}`);
    }
  }

  return response;
}

/**
 * Update money amount (update existing price)
 */
export async function updateMoneyAmount(id: string, data: {
  amount: number;
  compareAmount?: number;
}) {
  const query = `
    mutation UpdateMoneyAmount($id: ID!, $data: MoneyAmountUpdateInput!) {
      updateMoneyAmount(where: { id: $id }, data: $data) {
        id
        amount
        compareAmount
        currency {
          code
        }
        region {
          id
          code
          name
        }
      }
    }
  `;

  const response = await keystoneClient(query, { id, data });

  if (response.success) {
    // Get product ID for revalidation
    const productQuery = `
      query GetProductFromPrice($id: ID!) {
        moneyAmount(where: { id: $id }) {
          productVariant {
            product {
              id
            }
          }
        }
      }
    `;
    
    const productResponse = await keystoneClient(productQuery, { id });
    if (productResponse.success && productResponse.data.moneyAmount?.productVariant?.product?.id) {
      revalidatePath(`/dashboard/platform/products/${productResponse.data.moneyAmount.productVariant.product.id}`);
    }
  }

  return response;
}
