export interface Discount {
  id: string;
  code: string;
  isDynamic: boolean;
  isDisabled: boolean;
  stackable: boolean;
  startsAt: string;
  endsAt?: string | null;
  metadata?: any;
  usageLimit?: number | null;
  usageCount: number;
  validDuration?: string | null;
  discountRule?: {
    id: string;
    description?: string | null;
    type: 'fixed' | 'percentage' | 'free_shipping';
    value: number;
    allocation?: string | null;
    products?: Array<{
      id: string;
      title: string;
    }>;
    discountConditions?: Array<{
      id: string;
      type: string;
      operator: string;
    }>;
  } | null;
  regions?: Array<{
    id: string;
    name: string;
  }>;
  orders?: Array<{
    id: string;
    orderNumber: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface DiscountFilters {
  search?: string;
  type?: string;
  isDisabled?: boolean;
  stackable?: boolean;
  hasExpired?: boolean;
}

export interface DiscountStatusCounts {
  all: number;
  active: number;
  disabled: number;
  expired: number;
}