import { Suspense } from 'react';
import { PageBreadcrumbs } from '@/features/dashboard/components/PageBreadcrumbs';
import { PlatformFilterBar } from '@/features/dashboard/components/PlatformFilterBar';
import { PaginationWrapper } from '@/features/dashboard/components/PaginationWrapper';
import { UserDetailsComponent } from '../components/UserDetailsComponent';
import { StatusTabs } from '../components/StatusTabs';
import { getUsers, getUserStatusCounts } from '../actions';
import { Triangle, Square, Circle } from 'lucide-react';

interface UsersListPageProps {
  searchParams: {
    page?: string;
    search?: string;
    sortBy?: string;
    status?: string;
    role?: string;
    team?: string;
    hasAccount?: string;
    onboardingStatus?: string;
  };
}

async function UsersContent({ searchParams }: UsersListPageProps) {
  const page = parseInt(searchParams.page || '1');
  const search = searchParams.search || '';
  const sortBy = searchParams.sortBy || 'createdAt_DESC';
  const status = searchParams.status || 'all';

  const filters: any = {};
  if (status === 'active') {
    filters.hasAccount = true;
    filters.onboardingStatus = 'completed';
  } else if (status === 'inactive') {
    filters.hasAccount = false;
  } else if (status === 'onboarding') {
    filters.onboardingStatus = 'in_progress';
  }

  if (searchParams.role) filters.role = searchParams.role;
  if (searchParams.team) filters.team = searchParams.team;

  const [{ users, totalCount }, statusCounts] = await Promise.all([
    getUsers({ page, search, sortBy, filters }),
    getUserStatusCounts()
  ]);

  const totalPages = Math.ceil(totalCount / 20);

  return (
    <>
      <div className="mb-6">
        <StatusTabs
          counts={statusCounts}
          currentStatus={status}
          onStatusChange={() => {}}
        />
      </div>

      {users.length === 0 ? (
        <div className="text-center py-12">
          <div className="flex justify-center items-center mb-4 space-x-2">
            <Triangle className="w-8 h-8 text-gray-300 animate-pulse" />
            <Square className="w-8 h-8 text-gray-300 animate-pulse delay-75" />
            <Circle className="w-8 h-8 text-gray-300 animate-pulse delay-150" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            No users found
          </h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {search ? 'Try adjusting your search terms' : 'Create your first user to get started'}
          </p>
        </div>
      ) : (
        <>
          <div className="grid gap-4">
            {users.map((user) => (
              <UserDetailsComponent key={user.id} user={user} />
            ))}
          </div>

          <div className="mt-8">
            <PaginationWrapper
              currentPage={page}
              totalPages={totalPages}
              basePath="/platform/users"
            />
          </div>
        </>
      )}
    </>
  );
}

export default async function UsersListPage({ searchParams }: UsersListPageProps) {
  return (
    <div className="container mx-auto py-8">
      <PageBreadcrumbs
        items={[
          { label: 'Dashboard', href: '/dashboard' },
          { label: 'Platform', href: '/dashboard/platform' },
          { label: 'Users' }
        ]}
      />

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Users
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Manage customer accounts, profiles, and user permissions.
        </p>
      </div>

      <div className="mb-6">
        <PlatformFilterBar
          searchPlaceholder="Search users by name, email, or phone..."
          sortOptions={[
            { label: 'Newest First', value: 'createdAt_DESC' },
            { label: 'Oldest First', value: 'createdAt_ASC' },
            { label: 'Name (A-Z)', value: 'name_ASC' },
            { label: 'Name (Z-A)', value: 'name_DESC' },
            { label: 'Email (A-Z)', value: 'email_ASC' },
            { label: 'Email (Z-A)', value: 'email_DESC' }
          ]}
        />
      </div>

      <Suspense fallback={<div>Loading users...</div>}>
        <UsersContent searchParams={searchParams} />
      </Suspense>
    </div>
  );
}