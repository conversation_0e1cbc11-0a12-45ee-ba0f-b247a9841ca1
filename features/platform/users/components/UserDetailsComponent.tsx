'use client';

import { useState } from 'react';
import { User } from '../lib/types';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { EditItemDrawer } from '@/features/dashboard/components/EditItemDrawer';
import { MapPin, Mail, Phone, Package, Users, Calendar } from 'lucide-react';
import { format } from 'date-fns';

interface UserDetailsComponentProps {
  user: User;
}

export function UserDetailsComponent({ user }: UserDetailsComponentProps) {
  const [isEditOpen, setIsEditOpen] = useState(false);

  const formatDate = (date: string) => {
    return format(new Date(date), 'MMM d, yyyy');
  };

  const getStatusBadge = () => {
    if (!user.hasAccount) {
      return <Badge variant="secondary">Inactive</Badge>;
    }
    if (user.onboardingStatus === 'in_progress' || user.onboardingStatus === 'not_started') {
      return <Badge variant="outline">Onboarding</Badge>;
    }
    return <Badge variant="default">Active</Badge>;
  };

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value={user.id} className="border-none">
            <AccordionTrigger className="hover:no-underline px-6 py-4">
              <div className="flex items-center justify-between w-full pr-4">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                    {user.name?.charAt(0).toUpperCase() || 'U'}
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                      {user.name}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {user.email}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  {getStatusBadge()}
                  {user.role && (
                    <Badge variant="outline" className="hidden sm:inline-flex">
                      {user.role.name}
                    </Badge>
                  )}
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4">
              <div className="space-y-4">
                <div className="flex gap-4 text-sm text-gray-600 dark:text-gray-400">
                  {user.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      {user.phone}
                    </div>
                  )}
                  {user.team && (
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      {user.team.name}
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Joined {formatDate(user.createdAt)}
                  </div>
                </div>

                {user.addresses && user.addresses.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      Addresses ({user.addresses.length})
                    </h4>
                    <div className="space-y-2">
                      {user.addresses.slice(0, 2).map((address) => (
                        <div key={address.id} className="text-sm text-gray-600 dark:text-gray-400">
                          <p>{address.line1}</p>
                          {address.line2 && <p>{address.line2}</p>}
                          <p>
                            {address.city}, {address.provinceCode} {address.postalCode}
                          </p>
                          <p>{address.countryCode}</p>
                        </div>
                      ))}
                      {user.addresses.length > 2 && (
                        <p className="text-sm text-gray-500">
                          +{user.addresses.length - 2} more addresses
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {user.orders && user.orders.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <Package className="w-4 h-4" />
                      Recent Orders ({user.orders.length})
                    </h4>
                    <div className="space-y-2">
                      {user.orders.slice(0, 3).map((order) => (
                        <div key={order.id} className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">
                            #{order.orderNumber}
                          </span>
                          <span className="font-medium">
                            ${(order.total / 100).toFixed(2)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {user.customerGroups && user.customerGroups.length > 0 && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-sm mb-2">Customer Groups</h4>
                    <div className="flex flex-wrap gap-2">
                      {user.customerGroups.map((group) => (
                        <Badge key={group.id} variant="secondary">
                          {group.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex justify-end pt-2">
                  <Button
                    onClick={() => setIsEditOpen(true)}
                    variant="outline"
                    size="sm"
                  >
                    Edit User
                  </Button>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      <EditItemDrawer
        isOpen={isEditOpen}
        onClose={() => setIsEditOpen(false)}
        itemId={user.id}
        listKey="User"
      />
    </>
  );
}