'use client';

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { UserStatusCounts } from '../lib/types';

interface StatusTabsProps {
  counts: UserStatusCounts;
  currentStatus: string;
  onStatusChange: (status: string) => void;
}

export function StatusTabs({ counts, currentStatus, onStatusChange }: StatusTabsProps) {
  return (
    <Tabs value={currentStatus} onValueChange={onStatusChange}>
      <TabsList>
        <TabsTrigger value="all">
          All Users
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.all}
          </span>
        </TabsTrigger>
        <TabsTrigger value="active">
          Active
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.active}
          </span>
        </TabsTrigger>
        <TabsTrigger value="inactive">
          Inactive
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.inactive}
          </span>
        </TabsTrigger>
        <TabsTrigger value="onboarding">
          Onboarding
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full">
            {counts.onboarding}
          </span>
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}