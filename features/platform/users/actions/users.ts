'use server';

import { getAuthenticatedUser } from '@/features/dashboard/hooks/getAuthenticatedUser';
import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';
import { UserFilters, UserStatusCounts } from '../lib/types';

export async function getUsers({
  page = 1,
  perPage = 20,
  search = '',
  sortBy = 'createdAt_DESC',
  filters = {}
}: {
  page?: number;
  perPage?: number;
  search?: string;
  sortBy?: string;
  filters?: UserFilters;
}) {
  const authenticatedUser = await getAuthenticatedUser();
  
  const where: any = {};
  
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { phone: { contains: search, mode: 'insensitive' } }
    ];
  }
  
  if (filters.role) {
    where.role = { id: { equals: filters.role } };
  }
  
  if (filters.team) {
    where.team = { id: { equals: filters.team } };
  }
  
  if (filters.hasAccount !== undefined) {
    where.hasAccount = { equals: filters.hasAccount };
  }
  
  if (filters.onboardingStatus) {
    where.onboardingStatus = { equals: filters.onboardingStatus };
  }
  
  const response = await keystoneClient(authenticatedUser?.apiKey).request(`
    query UsersQuery($where: UserWhereInput, $orderBy: [UserOrderByInput!], $take: Int, $skip: Int) {
      users(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
        id
        name
        email
        phone
        hasAccount
        onboardingStatus
        firstName
        lastName
        role {
          id
          name
        }
        team {
          id
          name
        }
        addresses {
          id
          line1
          line2
          city
          provinceCode
          postalCode
          countryCode
        }
        orders {
          id
          orderNumber
          total
          createdAt
        }
        customerGroups {
          id
          name
        }
        createdAt
        updatedAt
      }
      usersCount(where: $where)
    }
  `, {
    where,
    orderBy: [sortBy],
    take: perPage,
    skip: (page - 1) * perPage
  });
  
  return {
    users: response.users || [],
    totalCount: response.usersCount || 0
  };
}

export async function getUserStatusCounts() {
  const authenticatedUser = await getAuthenticatedUser();
  
  const response = await keystoneClient(authenticatedUser?.apiKey).request(`
    query UserStatusCounts {
      all: usersCount
      active: usersCount(where: { hasAccount: { equals: true } })
      inactive: usersCount(where: { hasAccount: { equals: false } })
      onboarding: usersCount(where: { onboardingStatus: { in: ["in_progress", "not_started"] } })
    }
  `);
  
  return {
    all: response.all || 0,
    active: response.active || 0,
    inactive: response.inactive || 0,
    onboarding: response.onboarding || 0
  };
}

export async function getRoles() {
  const authenticatedUser = await getAuthenticatedUser();
  
  const response = await keystoneClient(authenticatedUser?.apiKey).request(`
    query GetRoles {
      roles {
        id
        name
      }
    }
  `);
  
  return response.roles || [];
}

export async function getTeams() {
  const authenticatedUser = await getAuthenticatedUser();
  
  const response = await keystoneClient(authenticatedUser?.apiKey).request(`
    query GetTeams {
      teams {
        id
        name
      }
    }
  `);
  
  return response.teams || [];
}