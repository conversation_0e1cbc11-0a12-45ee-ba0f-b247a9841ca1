export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string | null;
  hasAccount: boolean;
  onboardingStatus?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  role?: {
    id: string;
    name: string;
  } | null;
  team?: {
    id: string;
    name: string;
  } | null;
  addresses?: Array<{
    id: string;
    line1: string;
    line2?: string | null;
    city: string;
    provinceCode?: string | null;
    postalCode: string;
    countryCode: string;
  }>;
  orders?: Array<{
    id: string;
    orderNumber: string;
    total: number;
    createdAt: string;
  }>;
  customerGroups?: Array<{
    id: string;
    name: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface UserFilters {
  search?: string;
  role?: string;
  team?: string;
  hasAccount?: boolean;
  onboardingStatus?: string;
}

export interface UserStatusCounts {
  all: number;
  active: number;
  inactive: number;
  onboarding: number;
}