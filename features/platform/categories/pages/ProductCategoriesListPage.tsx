import { getListByPath } from "@/features/dashboard/actions";
import { getFilteredProductCategories, getProductCategoryStatusCounts } from "../actions";
import { PageBreadcrumbs } from "@/features/dashboard/components/PageBreadcrumbs";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Triangle, Square, Circle } from "lucide-react";
import { ProductCategoryDetailsComponent } from "../components/ProductCategoryDetailsComponent";
import { StatusTabs } from "../components/StatusTabs";
import { PlatformFilterBar } from '@/features/dashboard/components/PlatformFilterBar';
import type { SortOption } from '@/features/dashboard/components/PlatformFilterBar';
import { PaginationWrapper } from "@/features/dashboard/components/PaginationWrapper";
import type { ProductCategory, ProductCategoryStatusCounts } from "../lib/types";

interface PageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

function ErrorDisplay({ title, message }: { title: string; message: string }) {
  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-2xl font-bold tracking-tight text-red-600">
        {title}
      </h1>
      <p className="mt-2 text-gray-600">{message}</p>
    </div>
  );
}

export async function ProductCategoriesListPage({ searchParams }: PageProps) {
  const resolvedSearchParams = await searchParams;
  // Parse search parameters
  const page = Number(resolvedSearchParams.page) || 1;
  const pageSize = Number(resolvedSearchParams.pageSize) || 10;

  // Get status filter from URL
  let status: boolean | null = null;
  const statusFilter = resolvedSearchParams["!status_matches"];
  if (statusFilter) {
    try {
      const parsed = JSON.parse(decodeURIComponent(statusFilter as string));
      if (Array.isArray(parsed) && parsed.length > 0) {
        status = parsed[0].value;
      }
    } catch (e) {
      // Invalid JSON in URL, ignore
    }
  }

  // Get search term from URL
  const search = typeof resolvedSearchParams.search === "string" && resolvedSearchParams.search !== "" ? resolvedSearchParams.search : null;

  try {
    // Get list metadata
    const list = await getListByPath("product-categories");

    if (!list) {
      return (
        <ErrorDisplay
          title="Invalid List"
          message="The requested list could not be found."
        />
      );
    }

    // Get sort from URL
    const sortBy = resolvedSearchParams.sortBy as string | undefined;
    const sort = sortBy ? {
      field: sortBy.startsWith("-") ? sortBy.slice(1) : sortBy,
      direction: sortBy.startsWith("-") ? "DESC" : "ASC"
    } as SortOption : null;

    // Fetch product categories with filters
    const response = await getFilteredProductCategories(
      status,
      search,
      page,
      pageSize,
      sort
    );

    let categories: ProductCategory[] = [];
    let count = 0;

    if (response.success) {
      // Ensure data exists and has the expected properties
      categories = response.data?.items || [];
      count = response.data?.count || 0;
    } else {
      // Log the error and use fallback values
      console.error("Error fetching product categories:", response.error);
    }

    // Get status counts
    const statusCountsResponse = await getProductCategoryStatusCounts();
    let statusCounts: ProductCategoryStatusCounts = {
      active: 0,
      inactive: 0,
      all: 0,
    };

    if (statusCountsResponse.success) {
      statusCounts = statusCountsResponse.data;
    } else {
      console.error("Error fetching product category status counts:", statusCountsResponse.error);
    }

    return (
      <section
        aria-label="Product Categories overview"
        className="h-screen overflow-hidden flex flex-col"
      >
        <PageBreadcrumbs
          items={[
            {
              type: "link",
              label: "Dashboard",
              href: "/dashboard",
            },
            {
              type: "page",
              label: "Platform",
              showModelSwitcher: true,
              switcherType: "platform",
            },
            {
              type: "page",
              label: "Product Categories",
            },
          ]}
        />

        <div className="flex flex-col flex-1 min-h-0">

          <div className="border-gray-200 dark:border-gray-800">
            <div className="px-4 md:px-6 pt-4 md:pt-6 pb-4">
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-50">
                Product Categories
              </h1>
              <p className="text-muted-foreground">
                <span>Manage your product categories and their hierarchy.</span>
              </p>
            </div>
          </div>

          <PlatformFilterBar
            list={{
              key: list.key,
              path: list.path,
              label: list.label,
              singular: list.singular,
              plural: list.plural,
              description: list.description || undefined,
              labelField: list.labelField as string,
              initialColumns: list.initialColumns,
              groups: list.groups as unknown as string[],
              graphql: {
                plural: list.plural,
                singular: list.singular
              },
              fields: list.fields
            }}
            currentSort={sort}
          />

          <div className="border-b">
            <StatusTabs
              statusCounts={{
                all: statusCounts.all || 0,
                active: statusCounts.active || 0,
                inactive: statusCounts.inactive || 0
              }}
            />
          </div>

          <div className="flex-1 overflow-auto pb-4">
            {categories && categories.length > 0 ? (
              <div className="grid grid-cols-1 divide-y">
                {categories.map((category: ProductCategory) => (
                  <ProductCategoryDetailsComponent key={category.id} category={category} />
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="relative h-11 w-11 mx-auto mb-2">
                    <Triangle className="absolute left-1 top-1 w-4 h-4 fill-indigo-200 stroke-indigo-400 dark:stroke-indigo-600 dark:fill-indigo-950 rotate-[90deg]" />
                    <Square className="absolute right-[.2rem] top-1 w-4 h-4 fill-orange-300 stroke-orange-500 dark:stroke-amber-600 dark:fill-amber-950 rotate-[30deg]" />
                    <Circle className="absolute bottom-2 left-1/2 -translate-x-1/2 w-4 h-4 fill-emerald-200 stroke-emerald-400 dark:stroke-emerald-600 dark:fill-emerald-900" />
                  </div>
                  <p className="font-medium">No product categories found</p>
                  <p className="text-muted-foreground text-sm">
                    {(search !== null) || status !== null
                      ? "Try adjusting your search or filter criteria"
                      : "Create your first product category to get started"}
                  </p>
                  {((search !== null) || status !== null) && (
                    <Link href="/dashboard/platform/product-categories">
                      <Button variant="outline" className="mt-4" size="sm">
                        Clear filters
                      </Button>
                    </Link>
                  )}
                </div>
              </div>
            )}
          </div>

          <PaginationWrapper
            currentPage={page}
            total={count}
            pageSize={pageSize}
            list={{
              singular: "product category",
              plural: "product categories",
              path: "product-categories",
              gqlNames: {
                deleteMutationName: list.gqlNames?.deleteMutationName || '',
                listQueryName: list.gqlNames?.listQueryName || '',
                itemQueryName: list.gqlNames?.itemQueryName || '',
                listQueryCountName: list.gqlNames?.listQueryCountName || '',
                listOrderName: list.gqlNames?.listOrderName || '',
                updateMutationName: list.gqlNames?.updateMutationName || '',
                createMutationName: list.gqlNames?.createMutationName || '',
                whereInputName: list.gqlNames?.whereInputName || '',
                whereUniqueInputName: list.gqlNames?.whereUniqueInputName || '',
                updateInputName: list.gqlNames?.updateInputName || '',
                createInputName: list.gqlNames?.createInputName || ''
              }
            }}
          />
        </div>
      </section>
    );
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return (
      <ErrorDisplay
        title="Error Loading Product Categories"
        message={`There was an error loading product categories: ${errorMessage}`}
      />
    );
  }
}