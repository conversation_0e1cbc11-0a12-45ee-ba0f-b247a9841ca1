import { getProductCategory } from "@/features/platform/categories/actions"
import { ProductCategoryPageClient } from "./ProductCategoryPageClient"
import { PageBreadcrumbs } from "@/features/dashboard/components/PageBreadcrumbs"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { AlertTriangle, ArrowLeft, RefreshCw } from "lucide-react"
import Link from "next/link"

interface PageProps {
  params: { id: string }
  searchParams: { [key: string]: string | string[] | undefined }
}

function ErrorDisplay({ title, message, categoryId }: { title: string; message: string; categoryId?: string }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto">
          <Card className="border-red-200 dark:border-red-800">
            <CardContent className="pt-6 text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20 mb-4">
                <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <h1 className="text-xl font-semibold text-red-900 dark:text-red-100 mb-2">{title}</h1>
              <p className="text-red-700 dark:text-red-300 mb-4">{message}</p>
              {categoryId && (
                <p className="text-sm text-red-600 dark:text-red-400 mb-6 font-mono bg-red-50 dark:bg-red-900/10 px-2 py-1 rounded">
                  ID: {categoryId}
                </p>
              )}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link href="/dashboard/platform/product-categories">
                  <Button variant="outline" className="w-full sm:w-auto">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Categories
                  </Button>
                </Link>
                <Button variant="default" onClick={() => window.location.reload()} className="w-full sm:w-auto">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default async function ProductCategoryDetailPage({ params, searchParams }: PageProps) {
  const categoryId = params.id

  if (!categoryId) {
    return <ErrorDisplay title="Missing Category ID" message="No product category ID was provided in the URL." />
  }

  const response = await getProductCategory(categoryId)

  if (!response.success || !response.data) {
    return (
      <ErrorDisplay
        title="Error Loading Product Category"
        message={response.error || "The product category could not be loaded."}
        categoryId={categoryId}
      />
    )
  }

  const productCategoryData = response.data.productCategory

  if (!productCategoryData) {
    return (
      <ErrorDisplay
        title="Product Category Not Found"
        message="The requested product category does not exist or could not be retrieved."
        categoryId={categoryId}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gray-50/50 dark:bg-gray-900/50">
      <div className="sticky top-0 z-10 bg-white/80 dark:bg-gray-950/80 backdrop-blur-sm border-b">
        <div className="container mx-auto">
          <PageBreadcrumbs
            items={[
              { type: "link", label: "Dashboard", href: "/dashboard" },
              { type: "page", label: "Platform", showModelSwitcher: true, switcherType: "platform" },
              { type: "link", label: "Product Categories", href: "/dashboard/platform/product-categories" },
              { type: "page", label: productCategoryData.title },
            ]}
          />
        </div>
      </div>

      <main className="container mx-auto px-4 py-6">
        <ProductCategoryPageClient initialCategory={productCategoryData} searchParams={searchParams} />
      </main>
    </div>
  )
}
