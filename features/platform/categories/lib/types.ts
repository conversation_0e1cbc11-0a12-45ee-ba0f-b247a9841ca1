// Defines the shape of the sort option used in filtering and sorting data.
export interface SortOption {
  field: string;
  direction: "ASC" | "DESC";
}

// Represents a subset of the Product model, containing fields essential for display
// within the ProductCategory context, such as in a list of products for a category.
export interface ProductSubset {
  id: string;
  title: string;
  handle?: string;
  status: string; // e.g., "draft", "published", "archived"
  thumbnail?: string; // URL to the product's thumbnail image
  productType?: {
    id: string;
    value: string;
  };
  productVariants?: Array<{
    id: string;
    title: string;
    sku?: string;
    inventoryQuantity?: number;
  }>;
  // Add other fields if they become necessary for display, e.g., SKU, price
}

// Defines the main ProductCategory entity based on the Keystone model.
export interface ProductCategory {
  id: string;
  title: string;
  handle: string;
  metadata?: Record<string, any> | null; // JSON field
  isInternal: boolean;
  isActive: boolean;
  // Relationships
  products?: ProductSubset[]; // For displaying related products
  parentCategory?: Pick<ProductCategory, "id" | "title" | "handle"> | null;
  categoryChildren?: Pick<ProductCategory, "id" | "title" | "handle">[];
  // Meta fields that might be added by actions
  productsCount?: number; // From _productsMeta
  categoryChildrenCount?: number; // From _categoryChildrenMeta
  // Tracking fields (assuming these are standard across models)
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  // Potentially other fields from trackingFields if needed directly
}

// Represents the counts of product categories for each status,
// used by the StatusTabs component.
export interface ProductCategoryStatusCounts {
  all: number;
  active: number;
  inactive: number;
}

// Input type for updating a ProductCategory, typically used in mutations.
// This should align with the fields available in the `updateProductCategory` mutation.
export interface ProductCategoryUpdateInput {
  title?: string;
  handle?: string;
  metadata?: Record<string, any>;
  isInternal?: boolean;
  isActive?: boolean;
  parentCategoryId?: string | null; // For updating parent relationship
  // Add other updatable fields as necessary
}

// Input type for creating a ProductCategory.
export interface ProductCategoryCreateInput {
  title: string;
  handle?: string; // Optional, can be auto-generated
  metadata?: Record<string, any>;
  isInternal?: boolean;
  isActive?: boolean;
  parentCategoryId?: string | null;
}

// Generic API response structure for list operations
export interface ApiListResponse<T> {
  items: T[];
  count: number;
  // Potentially other metadata like pageInfo
}

// Generic API response structure for single item operations
export interface ApiItemResponse<T> {
  item: T | null;
}

// Specific response type for fetching filtered product categories
export interface FilteredProductCategoriesResponse {
  success: boolean;
  data?: ApiListResponse<ProductCategory>;
  error?: string;
}

// Specific response type for fetching a single product category
export interface ProductCategoryResponse {
  success: boolean;
  data?: ProductCategory; // Changed from ApiItemResponse for directness as per plan
  error?: string;
}

// Specific response type for status counts
export interface ProductCategoryStatusCountsResponse {
  success: boolean;
  data?: ProductCategoryStatusCounts;
  error?: string;
}

// Specific response type for fetching products for a category
export interface ProductsForCategoryResponse {
  success: boolean;
  data?: ApiListResponse<ProductSubset>;
  error?: string;
}

// Generic mutation response
export interface MutationResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}