"use client";

import React, { useEffect, useState, useTransition } from 'react';
import { Badge } from '@/components/ui/badge';
import { ProductSubset } from '../lib/types';
import { getProductsForCategory } from '../actions';
import { Button } from '@/components/ui/button';
import { PackageOpen, ChevronLeft, ChevronRight, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import { Skeleton } from '@/components/ui/skeleton'; // For loading state

interface AssociatedProductsListProps {
  categoryId: string;
}

export const AssociatedProductsList = ({ categoryId }: AssociatedProductsListProps) => {
  const [products, setProducts] = useState<ProductSubset[]>([]);
  const [isLoading, setIsLoading] = useState(true); // Start with loading true
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [isPending, startTransition] = useTransition();
  const pageSize = 5;

  useEffect(() => {
    const fetchProducts = () => {
      setIsLoading(true);
      startTransition(async () => {
        const response = await getProductsForCategory(categoryId, page, pageSize, null, null);
        if (response.success && response.data) {
          setProducts(response.data.items);
          setTotalCount(response.data.count);
        } else {
          console.error("Failed to fetch associated products:", response.error);
          setProducts([]);
          setTotalCount(0);
          // Optionally show a toast message here for the error
        }
        setIsLoading(false);
      });
    };
    fetchProducts();
  }, [categoryId, page, pageSize]);

  if (isLoading && products.length === 0) {
    return (
      <div className="space-y-2 py-4">
        {[...Array(3)].map((_, i) => (
          <Skeleton key={i} className="h-8 w-full" />
        ))}
      </div>
    );
  }

  if (!isLoading && products.length === 0) {
    return (
      <div className="text-center py-10 bg-background rounded-md">
        <PackageOpen className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No Products Found</h3>
        <p className="mt-1 text-sm text-muted-foreground">There are no products currently associated with this category.</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <h4 className="text-sm font-medium text-foreground mb-2">
        Associated Products <span className="text-muted-foreground text-xs">({totalCount})</span>
      </h4>
      <ul className="divide-y divide-border bg-background rounded-md border">
        {products.map(product => (
          <li key={product.id} className="px-3 py-2.5 text-sm flex justify-between items-center hover:bg-muted/50">
            <div className="flex flex-col">
                <span className="font-medium">{product.title}</span>
                <span className="text-xs text-muted-foreground flex items-center">
                  <Badge
                    variant={product.status === "published" ? "default" : "secondary"}
                    className={`py-0.5 px-1.5 text-[0.6rem] font-medium rounded-sm mr-1.5 ${
                      product.status === "published"
                        ? "bg-emerald-100 text-emerald-700 border-emerald-300 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-700"
                        : "bg-gray-100 text-gray-700 border-gray-300 dark:bg-gray-700/50 dark:text-gray-400 dark:border-gray-600"
                    }`}
                  >
                    {product.status}
                  </Badge>
                  <span className="mr-1.5">·</span>
                  <span>{product.handle}</span>
                </span>
            </div>
            <Button variant="ghost" size="sm" asChild className="text-xs">
              <Link href={`/dashboard/platform/products/${product.id}`} target="_blank">
                View <ExternalLink className="ml-1.5 h-3 w-3" />
              </Link>
            </Button>
          </li>
        ))}
      </ul>
      {totalCount > pageSize && (
        <div className="flex justify-between items-center mt-4 pt-3 border-t">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setPage(p => Math.max(1, p - 1))} 
            disabled={page === 1 || isLoading || isPending}
            className="text-xs"
          >
            <ChevronLeft className="mr-1.5 h-3.5 w-3.5" />
            Previous
          </Button>
          <span className="text-xs text-muted-foreground">
            Page {page} of {Math.ceil(totalCount / pageSize)}
          </span>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setPage(p => p + 1)} 
            disabled={(page * pageSize >= totalCount) || isLoading || isPending}
            className="text-xs"
          >
            Next
            <ChevronRight className="ml-1.5 h-3.5 w-3.5" />
          </Button>
        </div>
      )}
    </div>
  );
};

export default AssociatedProductsList; // Ensure default export